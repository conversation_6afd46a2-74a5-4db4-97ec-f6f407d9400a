# AstraLearn ConfigMaps and Secrets
# Phase 6: Production Configuration Management
apiVersion: v1
kind: ConfigMap
metadata:
  name: astralearn-config
  namespace: astralearn
  labels:
    app: astralearn
    environment: production
    phase: "phase-6"
data:
  # Application Configuration
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  PORT: "3001"
  
  # Database Configuration
  DB_HOST: "postgresql-service"
  DB_PORT: "5432"
  DB_NAME: "astralearn_prod"
  DB_POOL_SIZE: "20"
  DB_TIMEOUT: "30000"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  REDIS_TIMEOUT: "5000"
  
  # API Configuration
  API_VERSION: "v1"
  API_RATE_LIMIT: "1000"
  API_TIMEOUT: "30000"
  
  # CORS Configuration
  CORS_ORIGIN: "https://astralearn.com,https://app.astralearn.com"
  CORS_METHODS: "GET,POST,PUT,DELETE,OPTIONS"
  CORS_HEADERS: "Content-Type,Authorization,X-Requested-With"
  
  # AI Configuration
  AI_SERVICE_URL: "http://ai-service:8080"
  AI_TIMEOUT: "30000"
  AI_MAX_RETRIES: "3"
  AI_BATCH_SIZE: "10"
  
  # Analytics Configuration
  ANALYTICS_ENABLED: "true"
  ANALYTICS_BATCH_SIZE: "100"
  ANALYTICS_FLUSH_INTERVAL: "60000"
  
  # Monitoring Configuration
  METRICS_ENABLED: "true"
  METRICS_PORT: "9090"
  HEALTH_CHECK_INTERVAL: "30"
  
  # Security Configuration
  SESSION_TIMEOUT: "3600"
  JWT_EXPIRY: "86400"
  PASSWORD_MIN_LENGTH: "8"
  
  # Feature Flags
  GAMIFICATION_ENABLED: "true"
  ADAPTIVE_LEARNING_ENABLED: "true"
  ANALYTICS_DASHBOARD_ENABLED: "true"
  INSTRUCTOR_TOOLS_ENABLED: "true"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: astralearn
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for"';
        access_log /var/log/nginx/access.log main;
        
        # Performance
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 10M;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
        
        # Upstream backend
        upstream backend {
            least_conn;
            server astralearn-backend-service:3001 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        # HTTP to HTTPS redirect
        server {
            listen 80;
            server_name astralearn.com app.astralearn.com;
            return 301 https://$server_name$request_uri;
        }
        
        # Main application server
        server {
            listen 443 ssl http2;
            server_name astralearn.com app.astralearn.com;
            
            # SSL Configuration
            ssl_certificate /etc/nginx/ssl/tls.crt;
            ssl_certificate_key /etc/nginx/ssl/tls.key;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
            ssl_prefer_server_ciphers off;
            ssl_session_cache shared:SSL:10m;
            ssl_session_timeout 10m;
            
            # Security headers
            add_header X-Frame-Options DENY always;
            add_header X-Content-Type-Options nosniff always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;
            add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:;" always;
            
            # Frontend static files
            location / {
                root /usr/share/nginx/html;
                try_files $uri $uri/ /index.html;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
            
            # API endpoints
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            # WebSocket connections
            location /socket.io/ {
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Authentication endpoints (stricter rate limiting)
            location /api/auth/ {
                limit_req zone=login burst=5 nodelay;
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            # Metrics endpoint (restricted access)
            location /metrics {
                allow 10.0.0.0/8;
                allow **********/12;
                allow ***********/16;
                deny all;
                proxy_pass http://backend;
            }
        }
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: astralearn-secrets
  namespace: astralearn
  labels:
    app: astralearn
    environment: production
type: Opaque
data:
  # These should be replaced with actual base64-encoded values
  jwt-secret: <base64-encoded-jwt-secret>
  session-secret: <base64-encoded-session-secret>
  openai-api-key: <base64-encoded-openai-key>
  db-password: <base64-encoded-db-password>
  redis-password: <base64-encoded-redis-password>
  ssl-cert: <base64-encoded-ssl-cert>
  ssl-key: <base64-encoded-ssl-key>

---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: astralearn
type: kubernetes.io/tls
data:
  tls.crt: <base64-encoded-ssl-cert>
  tls.key: <base64-encoded-ssl-key>
