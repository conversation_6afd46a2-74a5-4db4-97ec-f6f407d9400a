# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/astralearn?retryWrites=true&w=majority
MONGODB_URI_TEST=mongodb+srv://username:<EMAIL>/astralearn_test?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Groq API Configuration
# 1. Go to https://console.groq.com/keys
# 2. Sign up/Sign in with your account
# 3. Click "Create API Key"
# 4. Copy your API key and paste it below (format: gsk_...)
GROQ_API_KEY=your-groq-api-key

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=uploads/

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
