# Docker Compose Configuration for AstraLearn
# Part of Phase 3 Step 3: Production Optimization & Advanced Features
# 
# Complete production-ready setup with:
# - Frontend (React + Nginx)
# - Backend (Node.js + Express)
# - Database (MongoDB)
# - Cache (Redis)
# - Reverse Proxy (Nginx)
# - Monitoring (Prometheus + Grafana)

version: '3.8'

services:
  # Frontend Service
  astralearn-frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
      target: production
    container_name: astralearn-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:5000
      - VITE_WEBSOCKET_URL=http://localhost:5000
      - VITE_CDN_URL=${CDN_URL:-}
      - VITE_IMAGE_OPTIMIZATION=${IMAGE_OPTIMIZATION:-true}
    networks:
      - astralearn-network
    depends_on:
      - astralearn-backend
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`localhost`)"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend Service
  astralearn-backend:
    build:
      context: ./server
      dockerfile: Dockerfile
      target: production
    container_name: astralearn-backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=mongodb://mongodb:27017/astralearn
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key}
      - GROQ_API_KEY=${GROQ_API_KEY}
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret}
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - backend-logs:/app/logs
      - backend-uploads:/app/uploads
    networks:
      - astralearn-network
    depends_on:
      - mongodb
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`localhost`) && PathPrefix(`/api`)"
      - "traefik.http.services.backend.loadbalancer.server.port=5000"
    healthcheck:
      test: ["CMD", "node", "health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: astralearn-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
      - MONGO_INITDB_DATABASE=astralearn
    volumes:
      - mongodb-data:/data/db
      - mongodb-config:/data/configdb
      - ./mongo-init:/docker-entrypoint-initdb.d:ro
    networks:
      - astralearn-network
    command: ["mongod", "--replSet", "rs0", "--bind_ip_all"]
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: astralearn-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - astralearn-network
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Reverse Proxy (Traefik)
  traefik:
    image: traefik:v3.0
    container_name: astralearn-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    environment:
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=true
      - TRAEFIK_PROVIDERS_DOCKER=true
      - TRAEFIK_PROVIDERS_DOCKER_EXPOSEDBYDEFAULT=false
      - TRAEFIK_ENTRYPOINTS_WEB_ADDRESS=:80
      - TRAEFIK_ENTRYPOINTS_WEBSECURE_ADDRESS=:443
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-data:/data
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
    networks:
      - astralearn-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.localhost`)"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: astralearn-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - prometheus-data:/prometheus
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - astralearn-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: astralearn-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - astralearn-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: astralearn-node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - astralearn-network

  # Cadvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: astralearn-cadvisor
    restart: unless-stopped
    ports:
      - "8081:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    networks:
      - astralearn-network

  # Elasticsearch for logging (optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: astralearn-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - astralearn-network
    profiles:
      - logging

  # Kibana for log visualization (optional)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: astralearn-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - astralearn-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

networks:
  astralearn-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mongodb-data:
    driver: local
  mongodb-config:
    driver: local
  redis-data:
    driver: local
  backend-logs:
    driver: local
  backend-uploads:
    driver: local
  traefik-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  elasticsearch-data:
    driver: local
