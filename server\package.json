{"name": "@astralearn/server", "version": "1.0.0", "description": "AstraLearn Backend API Server", "main": "src/index.js", "type": "module", "scripts": {"dev": "nodemon start-server.js", "start": "node start-server.js", "start:production": "NODE_ENV=production node start-server.js", "start:debug": "node --inspect start-server.js", "test": "vitest", "test:groq": "node test-groq.js", "setup:groq": "node setup-groq.js", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "seed": "node simplified-seeder.js", "seed:full": "node complete-seeder.js", "seed:minimal": "node minimal-seeder.js", "health": "node -e \"fetch('http://localhost:5000/health').then(r=>r.json()).then(console.log).catch(console.error)\""}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "express-validator": "^7.0.1", "groq-sdk": "^0.27.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.3.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.13", "redis": "^4.6.13", "simple-statistics": "^7.8.8", "socket.io": "^4.7.5", "zod": "^3.22.4"}, "devDependencies": {"eslint": "^8.57.0", "nodemon": "^3.1.0", "vitest": "^1.4.0"}}