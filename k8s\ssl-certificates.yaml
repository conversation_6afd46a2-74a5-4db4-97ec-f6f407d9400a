# SSL Certificate Management for AstraLearn
# Phase 6: Automated SSL certificate provisioning with cert-manager

# Cert-manager cluster issuer for Let's Encrypt
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
  labels:
    app: cert-manager
    environment: production
spec:
  acme:
    # The ACME server URL for Let's Encrypt's production environment
    server: https://acme-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: <EMAIL>
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-prod
    # Enable the HTTP-01 challenge provider
    solvers:
    - http01:
        ingress:
          class: nginx
          podTemplate:
            spec:
              nodeSelector:
                "kubernetes.io/os": linux
    - dns01:
        cloudflare:
          email: <EMAIL>
          apiKeySecretRef:
            name: cloudflare-api-key-secret
            key: api-key

---
# Staging issuer for testing
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
  labels:
    app: cert-manager
    environment: staging
spec:
  acme:
    # The ACME server URL for Let's Encrypt's staging environment
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: <EMAIL>
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-staging
    # Enable the HTTP-01 challenge provider
    solvers:
    - http01:
        ingress:
          class: nginx

---
# Certificate for production domain
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: astralearn-tls
  namespace: astralearn
  labels:
    app: astralearn
    environment: production
spec:
  # Secret name where the certificate will be stored
  secretName: astralearn-tls
  # Certificate issuer reference
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  # Domain names for the certificate
  dnsNames:
  - astralearn.com
  - www.astralearn.com
  - app.astralearn.com
  - api.astralearn.com
  - grafana.astralearn.com
  - prometheus.astralearn.com

---
# Certificate for staging domain
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: astralearn-staging-tls
  namespace: astralearn-staging
  labels:
    app: astralearn
    environment: staging
spec:
  secretName: astralearn-staging-tls
  issuerRef:
    name: letsencrypt-staging
    kind: ClusterIssuer
  dnsNames:
  - staging.astralearn.com
  - app-staging.astralearn.com
  - api-staging.astralearn.com

---
# Certificate for development domain
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: astralearn-dev-tls
  namespace: astralearn-dev
  labels:
    app: astralearn
    environment: development
spec:
  secretName: astralearn-dev-tls
  issuerRef:
    name: letsencrypt-staging
    kind: ClusterIssuer
  dnsNames:
  - dev.astralearn.com
  - app-dev.astralearn.com
  - api-dev.astralearn.com

---
# Cloudflare API Key secret for DNS challenge
apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-api-key-secret
  namespace: cert-manager
type: Opaque
data:
  api-key: <base64-encoded-cloudflare-api-key>

---
# Certificate renewal monitoring
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cert-manager-metrics
  namespace: cert-manager
  labels:
    app: cert-manager
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
  endpoints:
  - port: tcp-prometheus-servicemonitor
    interval: 60s
    path: /metrics

---
# Alert rules for certificate expiration
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cert-manager-alerts
  namespace: astralearn-monitoring
  labels:
    app: cert-manager
spec:
  groups:
  - name: cert-manager.rules
    rules:
    - alert: CertManagerCertExpiringSoon
      expr: certmanager_certificate_expiration_timestamp_seconds - time() < 604800  # 7 days
      for: 1h
      labels:
        severity: warning
        service: cert-manager
      annotations:
        summary: "Certificate expiring soon"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} will expire in less than 7 days"
    
    - alert: CertManagerCertNotReady
      expr: certmanager_certificate_ready_status == 0
      for: 10m
      labels:
        severity: critical
        service: cert-manager
      annotations:
        summary: "Certificate not ready"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} is not ready"
    
    - alert: CertManagerIssuerNotReady
      expr: certmanager_clusterissuer_ready_status == 0
      for: 10m
      labels:
        severity: critical
        service: cert-manager
      annotations:
        summary: "Cluster issuer not ready"
        description: "Cluster issuer {{ $labels.name }} is not ready"
