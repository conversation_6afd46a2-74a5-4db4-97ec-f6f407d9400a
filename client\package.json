{"name": "@astralearn/client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@tanstack/react-query": "^5.28.9", "@tensorflow/tfjs": "^4.22.0", "axios": "^1.6.8", "clsx": "^2.1.0", "framer-motion": "^12.16.0", "immer": "^10.1.1", "lucide-react": "^0.365.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.2", "react-router-dom": "^6.22.3", "recharts": "^2.15.3", "socket.io-client": "^4.7.5", "tailwind-merge": "^2.2.2", "zustand": "^4.5.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "vite": "^5.2.0", "vitest": "^1.4.0"}}