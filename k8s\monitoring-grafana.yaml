# Grafana Deployment for AstraLearn Monitoring
# Phase 6: Advanced dashboards and visualization
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: astralearn-monitoring
  labels:
    app: grafana
    component: visualization
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 472
        fsGroup: 472
      containers:
      - name: grafana
        image: grafana/grafana:9.3.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
          name: web
          protocol: TCP
        env:
        - name: GF_SECURITY_ADMIN_USER
          value: "admin"
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GF_DATABASE_TYPE
          value: "postgres"
        - name: GF_DATABASE_HOST
          value: "postgresql-service.astralearn:5432"
        - name: GF_DATABASE_NAME
          value: "grafana"
        - name: GF_DATABASE_USER
          value: "grafana"
        - name: GF_DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: db-password
        - name: GF_SESSION_PROVIDER
          value: "redis"
        - name: GF_SESSION_PROVIDER_CONFIG
          value: "addr=redis-service.astralearn:6379,pool_size=100,db=grafana"
        - name: GF_SECURITY_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: secret-key
        - name: GF_ANALYTICS_REPORTING_ENABLED
          value: "false"
        - name: GF_ANALYTICS_CHECK_FOR_UPDATES
          value: "false"
        - name: GF_INSTALL_PLUGINS
          value: "grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel,grafana-simple-json-datasource"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: grafana-data
          mountPath: /var/lib/grafana
        - name: grafana-config
          mountPath: /etc/grafana/grafana.ini
          subPath: grafana.ini
          readOnly: true
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
          readOnly: true
        - name: grafana-dashboards-config
          mountPath: /etc/grafana/provisioning/dashboards
          readOnly: true
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards
          readOnly: true
      volumes:
      - name: grafana-data
        persistentVolumeClaim:
          claimName: grafana-pvc
      - name: grafana-config
        configMap:
          name: grafana-config
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-dashboards-config
        configMap:
          name: grafana-dashboards-config
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboards

---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: astralearn-monitoring
  labels:
    app: grafana
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: web
  selector:
    app: grafana

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: astralearn-monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: astralearn-monitoring
type: Opaque
data:
  admin-password: <base64-encoded-admin-password>
  db-password: <base64-encoded-db-password>
  secret-key: <base64-encoded-secret-key>

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: astralearn-monitoring
data:
  grafana.ini: |
    [server]
    protocol = http
    http_port = 3000
    domain = grafana.astralearn.com
    root_url = https://grafana.astralearn.com
    
    [database]
    type = postgres
    
    [session]
    provider = redis
    
    [security]
    admin_user = admin
    cookie_secure = true
    cookie_samesite = strict
    
    [auth]
    disable_login_form = false
    disable_signout_menu = false
    
    [auth.anonymous]
    enabled = false
    
    [snapshots]
    external_enabled = false
    
    [alerting]
    enabled = true
    execute_alerts = true
    
    [metrics]
    enabled = true
    
    [log]
    mode = console
    level = info
    
    [panels]
    disable_sanitize_html = false
    
    [plugins]
    allow_loading_unsigned_plugins = ""

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: astralearn-monitoring
data:
  prometheus.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus-service:9090
      isDefault: true
      editable: false
      jsonData:
        timeInterval: "5s"
        queryTimeout: "60s"
        httpMethod: "POST"
    - name: Loki
      type: loki
      access: proxy
      url: http://loki-service:3100
      editable: false
      jsonData:
        maxLines: 1000

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards-config
  namespace: astralearn-monitoring
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'astralearn-dashboards'
      orgId: 1
      folder: 'AstraLearn'
      type: file
      disableDeletion: false
      updateIntervalSeconds: 30
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: astralearn-monitoring
data:
  astralearn-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "AstraLearn - System Overview",
        "description": "Comprehensive overview of AstraLearn platform metrics",
        "tags": ["astralearn", "overview"],
        "timezone": "browser",
        "editable": true,
        "graphTooltip": 0,
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "timepicker": {},
        "refresh": "30s",
        "version": 1,
        "panels": [
          {
            "id": 1,
            "title": "API Request Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(http_requests_total[5m])",
                "legendFormat": "Requests/sec"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "reqps",
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 100},
                    {"color": "red", "value": 500}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Response Time P95",
            "type": "stat",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                "legendFormat": "P95 Response Time"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "s",
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 1},
                    {"color": "red", "value": 3}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
          },
          {
            "id": 3,
            "title": "Error Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
                "legendFormat": "Error Rate %"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percent",
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 1},
                    {"color": "red", "value": 5}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}
          },
          {
            "id": 4,
            "title": "Active Users",
            "type": "stat",
            "targets": [
              {
                "expr": "astralearn_active_users",
                "legendFormat": "Active Users"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "short",
                "color": {
                  "mode": "palette-classic"
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
          },
          {
            "id": 5,
            "title": "CPU Usage by Pod",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(container_cpu_usage_seconds_total{namespace=\"astralearn\"}[5m]) * 100",
                "legendFormat": "{{ pod }}"
              }
            ],
            "yAxes": [
              {
                "label": "CPU %",
                "max": 100,
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 6,
            "title": "Memory Usage by Pod",
            "type": "graph",
            "targets": [
              {
                "expr": "container_memory_working_set_bytes{namespace=\"astralearn\"} / 1024 / 1024",
                "legendFormat": "{{ pod }}"
              }
            ],
            "yAxes": [
              {
                "label": "Memory MB",
                "min": 0
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ]
      }
    }
  
  astralearn-learning-analytics.json: |
    {
      "dashboard": {
        "id": null,
        "title": "AstraLearn - Learning Analytics",
        "description": "Learning analytics and student engagement metrics",
        "tags": ["astralearn", "learning", "analytics"],
        "timezone": "browser",
        "editable": true,
        "graphTooltip": 0,
        "time": {
          "from": "now-24h",
          "to": "now"
        },
        "refresh": "1m",
        "version": 1,
        "panels": [
          {
            "id": 1,
            "title": "Daily Active Learners",
            "type": "graph",
            "targets": [
              {
                "expr": "astralearn_daily_active_users",
                "legendFormat": "Active Learners"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Course Completion Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "astralearn_course_completion_rate * 100",
                "legendFormat": "Completion Rate %"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percent",
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": null},
                    {"color": "yellow", "value": 50},
                    {"color": "green", "value": 80}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Average Session Duration",
            "type": "stat",
            "targets": [
              {
                "expr": "astralearn_avg_session_duration / 60",
                "legendFormat": "Avg Duration (min)"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "min"
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
          }
        ]
      }
    }
