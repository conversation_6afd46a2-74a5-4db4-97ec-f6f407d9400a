# AstraLearn Monitoring Stack
# Phase 6: Prometheus, Graf<PERSON>, and Loki for comprehensive monitoring
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: astralearn-monitoring
  labels:
    app: prometheus
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
      - name: prometheus
        image: prom/prometheus:v2.40.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9090
          name: web
          protocol: TCP
        args:
        - '--config.file=/etc/prometheus/prometheus.yml'
        - '--storage.tsdb.path=/prometheus'
        - '--storage.tsdb.retention.time=30d'
        - '--web.console.libraries=/etc/prometheus/console_libraries'
        - '--web.console.templates=/etc/prometheus/consoles'
        - '--web.enable-lifecycle'
        - '--web.route-prefix=/'
        - '--web.enable-admin-api'
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 4
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
          readOnly: true
        - name: prometheus-data
          mountPath: /prometheus
        - name: prometheus-rules
          mountPath: /etc/prometheus/rules
          readOnly: true
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-data
        persistentVolumeClaim:
          claimName: prometheus-pvc
      - name: prometheus-rules
        configMap:
          name: prometheus-rules

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: astralearn-monitoring
  labels:
    app: prometheus
spec:
  type: ClusterIP
  ports:
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: web
  selector:
    app: prometheus

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: astralearn-monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: astralearn-monitoring

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-pvc
  namespace: astralearn-monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: astralearn-monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'astralearn-production'
        environment: 'production'
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      # Prometheus itself
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']
      
      # Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https
      
      # Kubernetes nodes
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
        - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics
      
      # AstraLearn Backend
      - job_name: 'astralearn-backend'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - astralearn
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: astralearn-backend-service
        - source_labels: [__meta_kubernetes_endpoint_port_name]
          action: keep
          regex: metrics
        - source_labels: [__meta_kubernetes_namespace]
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_service_name]
          target_label: kubernetes_name
        - source_labels: [__meta_kubernetes_pod_name]
          target_label: kubernetes_pod_name
      
      # PostgreSQL
      - job_name: 'postgresql'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - astralearn
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: postgresql-service
        - source_labels: [__meta_kubernetes_namespace]
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_service_name]
          target_label: kubernetes_name
      
      # Redis
      - job_name: 'redis'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - astralearn
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: redis-service
        - source_labels: [__meta_kubernetes_namespace]
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_service_name]
          target_label: kubernetes_name
      
      # Nginx Ingress Controller
      - job_name: 'nginx-ingress'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
          action: keep
          regex: ingress-nginx
        - source_labels: [__meta_kubernetes_pod_container_port_number]
          action: keep
          regex: "10254"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: astralearn-monitoring
data:
  astralearn-rules.yml: |
    groups:
    - name: astralearn.rules
      rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: astralearn
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for {{ $labels.pod }} in namespace {{ $labels.namespace }}"
      
      # High memory usage
      - alert: HighMemoryUsage
        expr: (container_memory_working_set_bytes / container_spec_memory_limit_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: astralearn
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for {{ $labels.pod }} in namespace {{ $labels.namespace }}"
      
      # API response time
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 2m
        labels:
          severity: warning
          service: astralearn-api
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is above 2 seconds"
      
      # Error rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
          service: astralearn-api
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5%"
      
      # Database connection issues
      - alert: DatabaseConnectionIssues
        expr: postgresql_up == 0
        for: 1m
        labels:
          severity: critical
          service: postgresql
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database is not responding"
      
      # Redis connection issues
      - alert: RedisConnectionIssues
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis cache is not responding"
      
      # Pod restart frequency
      - alert: PodRestartingFrequently
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
          service: astralearn
        annotations:
          summary: "Pod restarting frequently"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"
