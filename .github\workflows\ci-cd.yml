# GitHub Actions CI/CD Pipeline for AstraLearn
# Part of Phase 3 Step 3: Production Optimization & Advanced Features
#
# Complete CI/CD pipeline with:
# - Automated testing
# - Security scanning
# - Docker image building
# - Deployment to multiple environments
# - Performance monitoring

name: AstraLearn CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'feature/*' ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: astralearn

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: |
          client/package-lock.json
          server/package-lock.json

    # Frontend checks
    - name: Install frontend dependencies
      run: npm ci
      working-directory: ./client

    - name: Run frontend linting
      run: npm run lint
      working-directory: ./client

    - name: Run frontend type checking
      run: npx tsc --noEmit
      working-directory: ./client

    # Backend checks
    - name: Install backend dependencies
      run: npm ci
      working-directory: ./server

    - name: Run backend linting
      run: npm run lint
      working-directory: ./server

    - name: Run backend type checking
      run: npx tsc --noEmit
      working-directory: ./server

    # Security scanning
    - name: Run npm audit (Frontend)
      run: npm audit --audit-level=high
      working-directory: ./client
      continue-on-error: true

    - name: Run npm audit (Backend)
      run: npm audit --audit-level=high
      working-directory: ./server
      continue-on-error: true

    # SonarCloud analysis
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      with:
        args: >
          -Dsonar.projectKey=astralearn
          -Dsonar.organization=your-org
          -Dsonar.sources=client/src,server/src
          -Dsonar.javascript.lcov.reportPaths=client/coverage/lcov.info,server/coverage/lcov.info

  # Automated Testing
  test:
    name: Automated Testing
    runs-on: ubuntu-latest
    needs: code-quality

    services:
      mongodb:
        image: mongo:7.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd mongo
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: |
          client/package-lock.json
          server/package-lock.json

    # Frontend testing
    - name: Install frontend dependencies
      run: npm ci
      working-directory: ./client

    - name: Run frontend unit tests
      run: npm test -- --coverage --watchAll=false
      working-directory: ./client
      env:
        CI: true

    - name: Run frontend integration tests
      run: npm run test:integration
      working-directory: ./client
      continue-on-error: true

    # Backend testing
    - name: Install backend dependencies
      run: npm ci
      working-directory: ./server

    - name: Run backend unit tests
      run: npm test -- --coverage --watchAll=false
      working-directory: ./server
      env:
        CI: true
        MONGODB_URI: mongodb://localhost:27017/astralearn_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret

    - name: Run backend integration tests
      run: npm run test:integration
      working-directory: ./server
      env:
        MONGODB_URI: mongodb://localhost:27017/astralearn_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
      continue-on-error: true

    # End-to-end testing
    - name: Install Playwright
      run: npx playwright install --with-deps
      working-directory: ./client

    - name: Run E2E tests
      run: npm run test:e2e
      working-directory: ./client
      env:
        PLAYWRIGHT_TEST_BASE_URL: http://localhost:3000
      continue-on-error: true

    # Upload coverage reports
    - name: Upload frontend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        files: ./client/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

    - name: Upload backend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        files: ./server/coverage/lcov.info
        flags: backend
        name: backend-coverage

  # Phase 6: AI Validation Testing
  ai-validation:
    name: AI Validation Tests
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: astralearn_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd testSuite && npm ci

    - name: Setup test environment
      run: |
        cp .env.test.example .env.test || echo "No .env.test.example found"

    - name: Run AI validation tests
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: astralearn_test
        DB_USER: postgres
        DB_PASSWORD: testpassword
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        AI_API_KEY: ${{ secrets.TEST_AI_API_KEY }}
      run: |
        cd testSuite && npm run test -- tests/aiValidation.test.js

    - name: Upload AI validation reports
      uses: actions/upload-artifact@v3
      with:
        name: ai-validation-reports
        path: testSuite/reports/

  # Phase 6: Learning Path Simulation Testing
  learning-path-simulation:
    name: Learning Path Tests
    runs-on: ubuntu-latest
    needs: test

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: astralearn_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd testSuite && npm ci

    - name: Run learning path simulation tests
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: astralearn_test
        DB_USER: postgres
        DB_PASSWORD: testpassword
        REDIS_HOST: localhost
        REDIS_PORT: 6379
      run: |
        cd testSuite && npm run test -- tests/learningPathSimulation.test.js

    - name: Upload learning path reports
      uses: actions/upload-artifact@v3
      with:
        name: learning-path-reports
        path: testSuite/reports/

  # Phase 6: Performance Testing
  performance-testing:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd testSuite && npm ci

    - name: Start application stack
      run: |
        docker-compose -f docker-compose.prod.yml up -d postgres redis
        sleep 30

    - name: Run performance tests
      run: |
        cd testSuite && node performance/performanceTester.js

    - name: Upload performance reports
      uses: actions/upload-artifact@v3
      with:
        name: performance-reports
        path: testSuite/reports/

    - name: Check performance thresholds
      run: |
        cd testSuite && npm run check:performance-thresholds

  # Phase 6: Security Testing
  security-testing:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd testSuite && npm ci

    - name: Start application for security testing
      run: |
        docker-compose -f docker-compose.prod.yml up -d
        sleep 60

    - name: Run OWASP security tests
      run: |
        cd testSuite && node security/securityTester.js

    - name: Run container security scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'astralearn-backend:latest'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload security scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
      continue-on-error: true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: testSuite/reports/

  # Build Docker Images
  build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, ai-validation, learning-path-simulation, performance-testing, security-testing]
    if: github.event_name != 'pull_request'

    strategy:
      matrix:
        component: [frontend, backend]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}-${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.component == 'frontend' && 'client' || 'server' }}
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Performance Testing
  performance:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install dependencies
      run: |
        npm install -g lighthouse artillery @lhci/cli
        npm ci
      working-directory: ./client

    - name: Build application
      run: npm run build
      working-directory: ./client

    - name: Start application
      run: |
        npm run preview &
        sleep 10
      working-directory: ./client

    - name: Run Lighthouse CI
      run: lhci autorun
      working-directory: ./client
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

    - name: Run load testing
      run: artillery run performance/load-test.yml
      continue-on-error: true

  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Docker Scout CVEs
      uses: docker/scout-action@v1
      with:
        command: cves
        image: ${{ env.REGISTRY }}/${{ github.repository }}-frontend:${{ github.sha }}
        only-severities: critical,high
        write-comment: true
        github-token: ${{ secrets.GITHUB_TOKEN }}

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, performance, security]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    - name: Deploy to EKS staging
      run: |
        aws eks update-kubeconfig --region us-west-2 --name astralearn-staging
        helm upgrade --install astralearn-staging ./k8s/helm/astralearn \
          --namespace staging \
          --set image.tag=${{ github.sha }} \
          --set environment=staging \
          --values ./k8s/helm/values-staging.yaml

    - name: Run smoke tests
      run: |
        sleep 60  # Wait for deployment
        npm run test:smoke -- --baseUrl=https://staging.astralearn.com
      working-directory: ./client

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, performance, security]
    if: github.event_name == 'release'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    - name: Deploy to EKS production
      run: |
        aws eks update-kubeconfig --region us-west-2 --name astralearn-production
        helm upgrade --install astralearn-production ./k8s/helm/astralearn \
          --namespace production \
          --set image.tag=${{ github.event.release.tag_name }} \
          --set environment=production \
          --values ./k8s/helm/values-production.yaml

    - name: Run post-deployment tests
      run: |
        sleep 120  # Wait for deployment
        npm run test:smoke -- --baseUrl=https://astralearn.com
      working-directory: ./client

    - name: Update deployment status
      if: always()
      run: |
        curl -X POST "https://api.github.com/repos/${{ github.repository }}/deployments" \
          -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          -H "Accept: application/vnd.github.v3+json" \
          -d '{
            "ref": "${{ github.sha }}",
            "environment": "production",
            "auto_merge": false,
            "required_contexts": []
          }'

  # Post-deployment monitoring
  monitor:
    name: Post-deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')

    steps:
    - name: Setup monitoring alerts
      run: |
        echo "Setting up monitoring alerts for deployment"
        # Add monitoring setup commands here

    - name: Send Slack notification
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        text: |
          AstraLearn deployment completed!
          Environment: ${{ github.ref == 'refs/heads/develop' && 'staging' || 'production' }}
          Commit: ${{ github.sha }}
          Author: ${{ github.actor }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
