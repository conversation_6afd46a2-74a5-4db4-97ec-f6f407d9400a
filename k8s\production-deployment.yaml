# Kubernetes Deployment Configuration for AstraLearn
# Part of Phase 3 Step 3: Production Optimization & Advanced Features

apiVersion: v1
kind: Namespace
metadata:
  name: astralearn
  labels:
    name: astralearn
    environment: production

---
# ConfigMap for application configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: astralearn-config
  namespace: astralearn
data:
  NODE_ENV: "production"
  CORS_ORIGIN: "https://astralearn.com"
  MONGODB_URI: "mongodb://mongodb-service:27017/astralearn"
  REDIS_URL: "redis://redis-service:6379"

---
# Secret for sensitive data
apiVersion: v1
kind: Secret
metadata:
  name: astralearn-secrets
  namespace: astralearn
type: Opaque
data:
  jwt-secret: <base64-encoded-jwt-secret>
  groq-api-key: <base64-encoded-groq-key>
  session-secret: <base64-encoded-session-secret>

---
# Backend Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: astralearn-backend
  namespace: astralearn
  labels:
    app: astralearn-backend
    tier: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: astralearn-backend
  template:
    metadata:
      labels:
        app: astralearn-backend
        tier: backend
    spec:
      containers:
      - name: backend
        image: ghcr.io/your-org/astralearn-backend:latest
        ports:
        - containerPort: 5000
        env:
        - name: PORT
          value: "5000"
        envFrom:
        - configMapRef:
            name: astralearn-config
        - secretRef:
            name: astralearn-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: backend-logs
          mountPath: /app/logs
        - name: backend-uploads
          mountPath: /app/uploads
      volumes:
      - name: backend-logs
        persistentVolumeClaim:
          claimName: backend-logs-pvc
      - name: backend-uploads
        persistentVolumeClaim:
          claimName: backend-uploads-pvc
      imagePullSecrets:
      - name: ghcr-secret

---
# Frontend Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: astralearn-frontend
  namespace: astralearn
  labels:
    app: astralearn-frontend
    tier: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: astralearn-frontend
  template:
    metadata:
      labels:
        app: astralearn-frontend
        tier: frontend
    spec:
      containers:
      - name: frontend
        image: ghcr.io/your-org/astralearn-frontend:latest
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      imagePullSecrets:
      - name: ghcr-secret

---
# MongoDB Deployment
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb
  namespace: astralearn
  labels:
    app: mongodb
    tier: database
spec:
  serviceName: mongodb-service
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
        tier: database
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          value: "admin"
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: password
        - name: MONGO_INITDB_DATABASE
          value: "astralearn"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        livenessProbe:
          exec:
            command:
            - mongo
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
  volumeClaimTemplates:
  - metadata:
      name: mongodb-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
      storageClassName: fast-ssd

---
# Redis Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: astralearn
  labels:
    app: redis
    tier: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        tier: cache
    spec:
      containers:
      - name: redis
        image: redis:7.2-alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-data-pvc

---
# Backend Service
apiVersion: v1
kind: Service
metadata:
  name: astralearn-backend-service
  namespace: astralearn
  labels:
    app: astralearn-backend
spec:
  selector:
    app: astralearn-backend
  ports:
  - port: 5000
    targetPort: 5000
    protocol: TCP
    name: http
  type: ClusterIP

---
# Frontend Service
apiVersion: v1
kind: Service
metadata:
  name: astralearn-frontend-service
  namespace: astralearn
  labels:
    app: astralearn-frontend
spec:
  selector:
    app: astralearn-frontend
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  type: ClusterIP

---
# MongoDB Service
apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  namespace: astralearn
  labels:
    app: mongodb
spec:
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    protocol: TCP
    name: mongodb
  type: ClusterIP

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: astralearn
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  type: ClusterIP

---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: astralearn-ingress
  namespace: astralearn
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - astralearn.com
    - www.astralearn.com
    secretName: astralearn-tls
  rules:
  - host: astralearn.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: astralearn-backend-service
            port:
              number: 5000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: astralearn-frontend-service
            port:
              number: 3000
  - host: www.astralearn.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: astralearn-backend-service
            port:
              number: 5000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: astralearn-frontend-service
            port:
              number: 3000

---
# Horizontal Pod Autoscaler for backend
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: astralearn-backend-hpa
  namespace: astralearn
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: astralearn-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# Horizontal Pod Autoscaler for frontend
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: astralearn-frontend-hpa
  namespace: astralearn
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: astralearn-frontend
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# PersistentVolumeClaims
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-logs-pvc
  namespace: astralearn
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-uploads-pvc
  namespace: astralearn
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data-pvc
  namespace: astralearn
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
  storageClassName: fast-ssd
