<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Lesson Completion</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Lesson Completion Test Tool</h1>
    
    <div class="test-section info">
        <h3>Authentication Status</h3>
        <p>Token: <span id="tokenStatus">Checking...</span></p>
        <p>User: <span id="userStatus">Checking...</span></p>
        <button onclick="checkAuth()">Check Authentication</button>
        <button onclick="loginTest()">Test Login</button>
    </div>

    <div class="test-section">
        <h3>Course Data</h3>
        <button onclick="loadCourses()">Load Courses</button>
        <button onclick="loadCourseProgress()">Load Course Progress</button>
        <pre id="courseData">Click "Load Courses" to see course data...</pre>
    </div>

    <div class="test-section">
        <h3>Lesson Completion Test</h3>
        <div>
            <label>Course ID: <input type="text" id="courseId" placeholder="Enter course ID" style="width: 300px;"></label>
        </div>
        <div style="margin-top: 10px;">
            <label>Lesson ID: <input type="text" id="lessonId" placeholder="Enter lesson ID" style="width: 300px;"></label>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="testLessonCompletion()">Mark Lesson Complete</button>
            <button onclick="checkLessonStatus()">Check Lesson Status</button>
        </div>
        <pre id="completionResult">Click "Mark Lesson Complete" to test...</pre>
    </div>

    <div class="test-section">
        <h3>API Response Log</h3>
        <button onclick="clearLog()">Clear Log</button>
        <pre id="apiLog"></pre>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        function log(message) {
            const logElement = document.getElementById('apiLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('apiLog').textContent = '';
        }

        function getToken() {
            return localStorage.getItem('token');
        }

        async function makeAuthenticatedRequest(endpoint, options = {}) {
            const token = getToken();
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`${API_BASE}${endpoint}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            log(`${options.method || 'GET'} ${endpoint} - Status: ${response.status}`);
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorText}`);
            }

            return response.json();
        }

        async function checkAuth() {
            try {
                const token = getToken();
                document.getElementById('tokenStatus').textContent = token ? 'Present' : 'Missing';
                
                if (token) {
                    const user = await makeAuthenticatedRequest('/auth/validate');
                    document.getElementById('userStatus').textContent = user.user?.email || 'Valid token, user data available';
                    log('Authentication check successful');
                } else {
                    document.getElementById('userStatus').textContent = 'No token available';
                    log('No authentication token found');
                }
            } catch (error) {
                document.getElementById('userStatus').textContent = `Error: ${error.message}`;
                log(`Authentication check failed: ${error.message}`);
            }
        }

        async function loginTest() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('token', data.token);
                    log('Login successful, token stored');
                    await checkAuth();
                } else {
                    log(`Login failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`Login error: ${error.message}`);
            }
        }

        async function loadCourses() {
            try {
                const courses = await makeAuthenticatedRequest('/courses');
                document.getElementById('courseData').textContent = JSON.stringify(courses, null, 2);
                
                // Auto-fill first course and lesson IDs
                if (courses.courses && courses.courses.length > 0) {
                    const firstCourse = courses.courses[0];
                    document.getElementById('courseId').value = firstCourse._id;
                    
                    if (firstCourse.modules && firstCourse.modules.length > 0) {
                        const firstModule = firstCourse.modules[0];
                        if (firstModule.lessons && firstModule.lessons.length > 0) {
                            document.getElementById('lessonId').value = firstModule.lessons[0]._id;
                        }
                    }
                }
                
                log('Courses loaded successfully');
            } catch (error) {
                document.getElementById('courseData').textContent = `Error: ${error.message}`;
                log(`Failed to load courses: ${error.message}`);
            }
        }

        async function loadCourseProgress() {
            try {
                const courseId = document.getElementById('courseId').value;
                if (!courseId) {
                    alert('Please enter a course ID first');
                    return;
                }
                
                const progress = await makeAuthenticatedRequest(`/courses/${courseId}/progress`);
                document.getElementById('courseData').textContent = JSON.stringify(progress, null, 2);
                log('Course progress loaded successfully');
            } catch (error) {
                document.getElementById('courseData').textContent = `Error: ${error.message}`;
                log(`Failed to load course progress: ${error.message}`);
            }
        }

        async function testLessonCompletion() {
            try {
                const courseId = document.getElementById('courseId').value;
                const lessonId = document.getElementById('lessonId').value;
                
                if (!courseId || !lessonId) {
                    alert('Please enter both course ID and lesson ID');
                    return;
                }

                const result = await makeAuthenticatedRequest(`/courses/${courseId}/lessons/${lessonId}/complete`, {
                    method: 'POST',
                    body: JSON.stringify({
                        moduleIndex: 0,
                        lessonIndex: 0,
                        completed: true
                    })
                });

                document.getElementById('completionResult').textContent = JSON.stringify(result, null, 2);
                log('Lesson completion successful');
            } catch (error) {
                document.getElementById('completionResult').textContent = `Error: ${error.message}`;
                log(`Lesson completion failed: ${error.message}`);
            }
        }

        async function checkLessonStatus() {
            try {
                const courseId = document.getElementById('courseId').value;
                
                if (!courseId) {
                    alert('Please enter a course ID first');
                    return;
                }

                const progress = await makeAuthenticatedRequest(`/courses/${courseId}/progress`);
                document.getElementById('completionResult').textContent = JSON.stringify(progress, null, 2);
                log('Lesson status checked successfully');
            } catch (error) {
                document.getElementById('completionResult').textContent = `Error: ${error.message}`;
                log(`Failed to check lesson status: ${error.message}`);
            }
        }

        // Auto-check authentication on page load
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
