/**
 * Data Synchronization Provider
 * Centralized state management for real-time data synchronization across all components
 * Eliminates mock data and ensures consistent, real-time data flow
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from '../components/auth/AuthProvider';
import analyticsService from '../services/analyticsService';

const DataSyncContext = createContext();

export const useDataSync = () => {
  const context = useContext(DataSyncContext);
  if (!context) {
    throw new Error('useDataSync must be used within a DataSyncProvider');
  }
  return context;
};

export const DataSyncProvider = ({ children }) => {
  const { token, user, isAuthenticated } = useAuth();
  
  // Centralized state for all real data
  const [courses, setCourses] = useState([]);
  const [enrolledCourses, setEnrolledCourses] = useState([]);
  const [userProgress, setUserProgress] = useState({});
  const [analytics, setAnalytics] = useState({});
  const [lessons, setLessons] = useState({});
  const [learningPaths, setLearningPaths] = useState([]);
  const [studyGroups, setStudyGroups] = useState([]);
  const [discussions, setDiscussions] = useState([]);
  const [achievements, setAchievements] = useState([]);
  const [notifications, setNotifications] = useState([]);
  
  // Loading states
  const [loading, setLoading] = useState({
    courses: false,
    progress: false,
    analytics: false,
    lessons: false,
    learningPaths: false,
    studyGroups: false,
    discussions: false,
    achievements: false,
    notifications: false
  });

  // Error states
  const [errors, setErrors] = useState({});

  const API_BASE = 'http://localhost:5000/api';

  // Helper function for API calls
  const apiCall = useCallback(async (endpoint, options = {}) => {
    if (!isAuthenticated || !token) {
      throw new Error('Not authenticated');
    }

    const response = await fetch(`${API_BASE}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }, [token, isAuthenticated]);

  // Set loading state for specific data type
  const setDataLoading = useCallback((dataType, isLoading) => {
    setLoading(prev => ({ ...prev, [dataType]: isLoading }));
  }, []);

  // Set error for specific data type
  const setDataError = useCallback((dataType, error) => {
    setErrors(prev => ({ ...prev, [dataType]: error }));
  }, []);

  // Clear error for specific data type
  const clearDataError = useCallback((dataType) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[dataType];
      return newErrors;
    });
  }, []);

  // Fetch courses with real data
  const fetchCourses = useCallback(async (refresh = false) => {
    if (!refresh && courses && courses.length > 0) {
      console.log('📚 Using cached courses, count:', courses.length);
      return courses;
    }
    
    setDataLoading('courses', true);
    clearDataError('courses');
    
    try {
      const data = await apiCall('/courses');
      setCourses(data.courses || data);
      return data.courses || data;
    } catch (error) {
      console.error('Failed to fetch courses:', error);
      setDataError('courses', error.message);
      return [];
    } finally {
      setDataLoading('courses', false);
    }
  }, [apiCall]);

  // Remove the activeRequests state and related logic
  // const [activeRequests, setActiveRequests] = useState(new Set());

  // Fetch user progress with simplified logic and better error handling
  const fetchUserProgress = useCallback(async (refresh = false) => {
    console.log('� fetchUserProgress called, refresh:', refresh);
    
    // Simple cache check without complex dependencies
    if (!refresh && userProgress && Object.keys(userProgress).length > 0) {
      console.log('📋 Using cached user progress, keys:', Object.keys(userProgress).length);
      return userProgress;
    }
    
    setDataLoading('progress', true);
    clearDataError('progress');
    
    try {
      // Simplified API call - just get enrolled courses
      console.log('📚 Fetching enrolled courses...');
      const enrolledData = await apiCall('/courses/my/enrolled');
      const enrolled = enrolledData.enrolledCourses || enrolledData.courses || enrolledData || [];
      console.log('📚 Found enrolled courses:', enrolled.length);

      // Set enrolled courses with progress data
      setEnrolledCourses(enrolled);
      
      // Initialize progress object
      const progressObj = {};
      
      // Process enrolled courses with simplified structure
      for (const enrollmentData of enrolled) {
        try {
          const course = enrollmentData.course || enrollmentData.courseId || enrollmentData;
          const courseId = course._id || course.id || enrollmentData.courseId;
          
          if (!courseId) continue;
          
          // Fetch detailed progress for each course
          try {
            console.log(`📊 Fetching detailed progress for course ${courseId}`);
            const progressResponse = await apiCall(`/courses/${courseId}/progress`);
            
            if (progressResponse.success && progressResponse.progress) {
              const { overall, lessons } = progressResponse.progress;
              
              // Count total lessons from course structure
              let totalLessons = 0;
              if (course.modules && Array.isArray(course.modules)) {
                totalLessons = course.modules.reduce((total, module) => {
                  return total + (module.lessons ? module.lessons.length : 0);
                }, 0);
              }
              
              // Extract completed lessons from progress data
              const completedLessons = lessons ? lessons
                .filter(lesson => lesson.progressType === 'lesson_complete' && lesson.progressData?.completed)
                .map(lesson => lesson.lessonId) : [];
              
              progressObj[courseId] = {
                completionPercentage: overall?.completionPercentage || Math.round((completedLessons.length / Math.max(totalLessons, 1)) * 100),
                timeSpent: overall?.timeSpent || 0,
                completionStatus: overall?.completionStatus || 'not-started',
                lastUpdated: progressResponse.progress.lastUpdated || new Date().toISOString(),
                totalLessons,
                completedLessons,
                lessons: lessons || []
              };
              
              console.log(`✅ Detailed progress loaded for ${courseId}: ${completedLessons.length}/${totalLessons} lessons`);
            } else {
              // Fallback to basic progress structure
              progressObj[courseId] = {
                completionPercentage: 0,
                timeSpent: 0,
                completionStatus: 'enrolled',
                lastUpdated: new Date().toISOString(),
                totalLessons: 0,
                completedLessons: [],
                lessons: []
              };
            }
          } catch (progressError) {
            console.warn(`⚠️ Failed to fetch progress for course ${courseId}:`, progressError);
            // Use enrollment data as fallback
            progressObj[courseId] = {
              completionPercentage: enrollmentData.progressData?.completionPercentage || 0,
              timeSpent: enrollmentData.progressData?.timeSpent || 0,
              completionStatus: enrollmentData.progressData?.completionStatus || 'enrolled',
              lastUpdated: enrollmentData.timestamp || enrollmentData.enrolledAt || new Date().toISOString(),
              totalLessons: 0,
              completedLessons: [],
              lessons: []
            };
          }
          
          console.log(`📊 Processed progress for ${courseId}`);
        } catch (err) {
          console.error(`Failed to process course progress:`, err);
        }
      }
      
      console.log('📊 Final progress object:', progressObj);
      setUserProgress(progressObj);
      return progressObj;
    } catch (error) {
      console.error('Failed to fetch user progress:', error);
      setDataError('progress', error.message);
      return {};
    } finally {
      setDataLoading('progress', false);
    }
  }, [apiCall]); // Removed complex dependencies

  // Fetch analytics data
  const fetchAnalytics = useCallback(async (refresh = false) => {
    if (!refresh && Object.keys(analytics).length > 0) return analytics;
    
    setDataLoading('analytics', true);
    clearDataError('analytics');
    
    try {
      // Use the new analytics service
      const summaryData = await analyticsService.getSummary(refresh);
      const performanceData = await analyticsService.getPerformanceMetrics(30, null, refresh);
      const patternsData = await analyticsService.getLearningPatterns(30, 'overview', refresh);
      
      // Combine all analytics data
      const combinedData = {
        summary: summaryData,
        performance: performanceData?.metrics || {},
        patterns: patternsData?.patterns || {},
        lastUpdated: new Date().toISOString()
      };
      
      setAnalytics(combinedData);
      return combinedData;
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      setDataError('analytics', error.message);
      return {};
    } finally {
      setDataLoading('analytics', false);
    }
  }, []);

  // Fetch lessons for a course
  const fetchLessons = useCallback(async (courseId, refresh = false) => {
    if (!courseId) return [];
    
    if (!refresh && lessons[courseId]) return lessons[courseId];
    
    setDataLoading('lessons', true);
    clearDataError('lessons');
    
    try {
      const data = await apiCall(`/courses/${courseId}/lessons`);
      setLessons(prev => ({ ...prev, [courseId]: data.lessons || data }));
      return data.lessons || data;
    } catch (error) {
      console.error(`Failed to fetch lessons for course ${courseId}:`, error);
      setDataError('lessons', error.message);
      return [];
    } finally {
      setDataLoading('lessons', false);
    }
  }, [apiCall, lessons]);

  // Update lesson progress in real-time
  const updateLessonProgress = useCallback(async (courseId, lessonId, progressData) => {
    try {
      const data = await apiCall(`/courses/${courseId}/lessons/${lessonId}/complete`, {
        method: 'POST',
        body: JSON.stringify(progressData)
      });

      // Update local state immediately for real-time UI updates
      setUserProgress(prev => {
        const existingProgress = prev[courseId] || {};
        const existingCompletedLessons = existingProgress.completedLessons || [];
        
        // Add lesson to completed lessons if not already there
        const updatedCompletedLessons = existingCompletedLessons.includes(lessonId) 
          ? existingCompletedLessons 
          : [...existingCompletedLessons, lessonId];

        return {
          ...prev,
          [courseId]: {
            ...existingProgress,
            lessons: {
              ...existingProgress.lessons,
              [lessonId]: progressData
            },
            completedLessons: updatedCompletedLessons,
            // Update completion percentage based on completed lessons
            completionPercentage: existingProgress.totalLessons 
              ? Math.round((updatedCompletedLessons.length / existingProgress.totalLessons) * 100)
              : 0,
            lastUpdated: new Date().toISOString()
          }
        };
      });

      // Refresh course progress from server to ensure consistency
      setTimeout(async () => {
        try {
          const progressResponse = await apiCall(`/courses/${courseId}/progress`);
          if (progressResponse.success && progressResponse.progress) {
            setUserProgress(prev => ({
              ...prev,
              [courseId]: {
                ...prev[courseId],
                completionPercentage: progressResponse.progress.overall?.completionPercentage || 0,
                overallProgress: progressResponse.progress.overall?.completionPercentage || 0,
                totalLessons: progressResponse.progress.overall?.totalLessons || 0,
                completedLessons: progressResponse.progress.lessons?.map(l => l.lessonId) || prev[courseId]?.completedLessons || []
              }
            }));
          }
        } catch (error) {
          console.error('Failed to refresh course progress:', error);
          // Fallback to full refresh
          fetchUserProgress(true);
        }
      }, 500);

      return data;
    } catch (error) {
      console.error('Failed to update lesson progress:', error);
      throw error;
    }
  }, [apiCall, fetchUserProgress]);

  // Complete a lesson with real-time updates
  const completeLesson = useCallback(async (courseId, lessonId, completionData) => {
    try {
      const data = await apiCall(`/courses/${courseId}/lessons/${lessonId}/complete`, {
        method: 'POST',
        body: JSON.stringify(completionData)
      });

      // Update multiple state pieces for real-time synchronization
      setUserProgress(prev => ({
        ...prev,
        [courseId]: {
          ...prev[courseId],
          completedLessons: [...(prev[courseId]?.completedLessons || []), lessonId],
          overallProgress: data.progress?.overallProgress || prev[courseId]?.overallProgress
        }
      }));

      // Refresh analytics data to reflect new completion
      fetchAnalytics(true);

      return data;
    } catch (error) {
      console.error('Failed to complete lesson:', error);
      throw error;
    }
  }, [apiCall, fetchAnalytics]);

  // Enroll in a course with real-time updates
  const enrollInCourse = useCallback(async (courseId) => {
    try {
      const data = await apiCall(`/courses/${courseId}/enroll`, {
        method: 'POST'
      });

      // Update user progress and courses data immediately
      fetchUserProgress(true);
      fetchCourses(true);

      return data;
    } catch (error) {
      console.error('Failed to enroll in course:', error);
      throw error;
    }
  }, [apiCall, fetchUserProgress, fetchCourses]);

  // Update course progress with real-time data
  const updateCourseProgress = useCallback(async (courseId, progressData) => {
    if (!courseId) return null;
    
    try {
      const response = await apiCall(`/courses/${courseId}/progress`, {
        method: 'POST',
        body: JSON.stringify(progressData)
      });
      
      // Update local state to show progress immediately
      if (response) {
        setUserProgress(prev => ({
          ...prev,
          [courseId]: {
            ...prev[courseId],
            ...response,
            completionPercentage: progressData.progress || response.completionPercentage || prev[courseId]?.completionPercentage || 0
          }
        }));
      }
      
      return response;
    } catch (error) {
      console.error(`Failed to update progress for course ${courseId}:`, error);
      setDataError('progress', error.message);
      return null;
    }
  }, [apiCall, setDataError]);

  // Get course progress with real-time calculation
  const getCourseProgress = useCallback((courseId) => {
    if (!courseId) return { completed: 0, total: 0, percentage: 0 };
    
    const progress = userProgress[courseId];
    console.log(`📊 getCourseProgress for ${courseId}:`, progress);
    
    if (!progress) return { completed: 0, total: 0, percentage: 0 };

    // Handle different progress data formats
    let completed = 0;
    let total = 0;
    
    if (progress.completedModules && Array.isArray(progress.completedModules)) {
      completed = progress.completedModules.length;
    } else if (progress.completedLessons && Array.isArray(progress.completedLessons)) {
      completed = progress.completedLessons.length;
    } else if (progress.lessons && Array.isArray(progress.lessons)) {
      // Handle lessons object format
      completed = progress.lessons.filter(l => l.completed).length;
    }
    
    if (typeof progress.completionPercentage === 'number') {
      const result = { 
        completed, 
        total: progress.totalLessons || 0, 
        percentage: progress.completionPercentage 
      };
      console.log(`📈 Course progress result:`, result);
      return result;
    }
    
    total = progress.totalLessons || 0;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    const result = { completed, total, percentage };
    console.log(`📈 Course progress result:`, result);
    return result;
  }, [userProgress]);

  // Get real learning statistics
  const getLearningStats = useCallback(() => {
    console.log('📊 Getting learning stats from userProgress:', userProgress);
    console.log('📊 Getting learning stats from enrolledCourses:', enrolledCourses);

    const stats = {
      totalCoursesEnrolled: enrolledCourses.length || Object.keys(userProgress).length,
      totalLessonsCompleted: Object.values(userProgress).reduce(
        (sum, progress) => {
          // Handle different data structure formats
          const completed = progress.completedLessons?.length || 
                           progress.lessons?.filter(l => l.completed)?.length || 
                           0;
          return sum + completed;
        }, 0
      ),
      totalTimeSpent: Object.values(userProgress).reduce(
        (sum, progress) => {
          const timeSpent = progress.timeSpent || 
                           progress.totalTimeSpent || 
                           0;
          return sum + timeSpent;
        }, 0
      ),
      averageProgress: enrolledCourses.length > 0
        ? enrolledCourses.reduce(
            (sum, enrollment) => {
              const progressPercent = enrollment.progress || 0;
              return sum + progressPercent;
            }, 0
          ) / enrolledCourses.length
        : Object.keys(userProgress).length > 0
          ? Object.values(userProgress).reduce(
              (sum, progress) => {
                const progressPercent = progress.completionPercentage ||
                                      progress.overallProgress ||
                                      0;
                return sum + progressPercent;
              }, 0
            ) / Object.keys(userProgress).length
          : 0,
      currentStreak: Math.max(
        ...Object.values(userProgress).map(p => p.currentStreak || p.streak || 0), 0
      )
    };

    console.log('📈 Calculated learning stats:', stats);
    return stats;
  }, [userProgress, enrolledCourses]);

  // Generate course recommendations based on user progress and preferences
  const getRecommendations = useCallback(() => {
    if (!courses.length) return [];

    const enrolledCourseIds = Object.keys(userProgress);
    const availableCourses = courses.filter(course => 
      !enrolledCourseIds.includes(course._id) && !enrolledCourseIds.includes(course.id)
    );

    if (!availableCourses.length) return [];

    // Get user's enrolled course categories for preference matching
    const enrolledCategories = courses
      .filter(course => enrolledCourseIds.includes(course._id) || enrolledCourseIds.includes(course.id))
      .map(course => course.category)
      .filter(Boolean);

    // Score courses based on various factors
    const scoredCourses = availableCourses.map(course => {
      let score = 0;
      
      // Prefer courses in categories user has shown interest in
      if (enrolledCategories.includes(course.category)) {
        score += 5;
      }
      
      // Prefer courses with higher ratings
      if (course.rating) {
        score += course.rating;
      }
      
      // Prefer courses with more enrollments (popularity)
      if (course.enrollmentCount) {
        score += Math.min(course.enrollmentCount / 100, 3); // Max 3 points
      }
      
      // Prefer beginner-friendly courses if user is new
      if (enrolledCourseIds.length <= 2 && course.difficulty === 'beginner') {
        score += 2;
      }
      
      // Add consistency factor based on course rating and category match
      if (enrolledCategories.includes(course.category)) {
        score += 1.5;
      }
      
      return {
        ...course,
        score,
        reason: enrolledCategories.includes(course.category) 
          ? `Based on your interest in ${course.category}` 
          : course.difficulty === 'beginner' && enrolledCourseIds.length <= 2
            ? 'Perfect for getting started'
            : 'Popular with other learners',
        duration: course.duration || course.estimatedDuration || '4 hours'
      };
    });

    // Sort by score and return top recommendations
    return scoredCourses
      .sort((a, b) => b.score - a.score)
      .slice(0, 6); // Return top 6 recommendations
  }, [courses, userProgress]);

  // Initialize data when user authenticates
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('🔄 DataSync initializing for authenticated user:', user.email);
      // Fetch all essential data
      fetchCourses();
      fetchUserProgress();
      fetchAnalytics();
    } else {
      console.log('❌ DataSync not initializing - auth status:', isAuthenticated, 'user:', !!user);
    }
  }, [isAuthenticated, user, fetchCourses, fetchUserProgress, fetchAnalytics]);

  // Simplified periodic sync - disabled during lesson loading
  useEffect(() => {
    if (!isAuthenticated) return;

    // Check if currently in a lesson view to prevent conflicts
    const isInLessonView = window.location.pathname?.includes('lesson') || 
                          document.querySelector('[data-lesson-active]');
    
    if (isInLessonView) {
      console.log('🚫 Skipping periodic sync - lesson view detected');
      return;
    }

    const syncInterval = setInterval(() => {
      console.log('🔄 Periodic data refresh triggered');
      // Only refresh essential data
      fetchCourses(true).catch(err => console.error('Sync fetch courses error:', err));
    }, 300000); // 5 minutes

    return () => {
      console.log('🧹 Clearing sync interval');
      clearInterval(syncInterval);
    };
  }, [isAuthenticated, fetchCourses]); // Minimal dependencies

  const value = {
    // Data
    courses,
    enrolledCourses,
    userProgress,
    analytics,
    lessons,
    learningPaths,
    studyGroups,
    discussions,
    achievements,
    notifications,

    // Loading states
    loading,

    // Error states
    errors,

    // Data fetchers
    fetchCourses,
    fetchUserProgress,
    fetchAnalytics,
    fetchLessons,

    // Data mutators
    updateLessonProgress,
    completeLesson,
    enrollInCourse,
    updateCourseProgress,    // Data calculators
    getCourseProgress,
    getLearningStats,
    getRecommendations,

    // Utility functions
    clearDataError,
    setDataError,

    // Real-time indicators
    isDataSynced: Object.keys(errors).length === 0 && isAuthenticated,
    lastSyncTime: new Date()
  };

  return (
    <DataSyncContext.Provider value={value}>
      {children}
    </DataSyncContext.Provider>
  );
};

export default DataSyncProvider;
