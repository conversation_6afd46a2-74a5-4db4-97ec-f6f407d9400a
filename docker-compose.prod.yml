# Astra<PERSON>earn Docker Compose Configuration
# Enhanced for Phase 6: Testing & Deployment

version: '3.8'

networks:
  astralearn-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  uploads-data:
    driver: local
  prometheus-data:
  loki-data:
  grafana-data:

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: astralearn-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-astralearn}
      POSTGRES_USER: ${DB_USER:-astralearn}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-securepassword}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - astralearn-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-astralearn} -d ${DB_NAME:-astralearn}"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - no-new-privileges:true

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: astralearn-redis
    restart: unless-stopped
    command: >
      --requirepass ${REDIS_PASSWORD:-redispassword}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - astralearn-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    security_opt:
      - no-new-privileges:true

  # Backend API
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
      target: production
    container_name: astralearn-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-astralearn}
      DB_USER: ${DB_USER:-astralearn}
      DB_PASSWORD: ${DB_PASSWORD:-securepassword}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redispassword}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      AI_API_KEY: ${GROQ_API_KEY:-your-groq-api-key}
      AI_API_URL: ${AI_API_URL:-https://api.groq.com/openai/v1}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    volumes:
      - uploads-data:/app/uploads
      - ./server/logs:/app/logs
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    networks:
      - astralearn-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Frontend
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
      target: production
    container_name: astralearn-frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3001/api}
      REACT_APP_WS_URL: ${REACT_APP_WS_URL:-ws://localhost:3001}
      REACT_APP_VERSION: ${REACT_APP_VERSION:-1.0.0}
      REACT_APP_ENV: ${REACT_APP_ENV:-production}
    ports:
      - "${FRONTEND_PORT:-3000}:8080"
    networks:
      - astralearn-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Nginx Load Balancer (for production scaling)
  nginx:
    image: nginx:1.24-alpine
    container_name: astralearn-nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - astralearn-network
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true

  # Monitoring (Prometheus for metrics)
  prometheus:
    image: prom/prometheus:latest
    container_name: astralearn-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - astralearn-network
    security_opt:
      - no-new-privileges:true

  # Log aggregation (for production monitoring)
  loki:
    image: grafana/loki:latest
    container_name: astralearn-loki
    restart: unless-stopped
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./monitoring/loki-config.yaml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    ports:
      - "3100:3100"
    networks:
      - astralearn-network
    security_opt:
      - no-new-privileges:true

  # Visualization (Grafana for dashboards)
  grafana:
    image: grafana/grafana:latest
    container_name: astralearn-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3030:3000"
    networks:
      - astralearn-network
    depends_on:
      - prometheus
      - loki
  prometheus-data:
  loki-data:
  grafana-data:
