# Astra<PERSON>earn Helm Chart Values
# Phase 6: Helm deployment configuration for easy management

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: "fast-ssd"
  
# Application configuration
app:
  name: astralearn
  version: "2.0.0"
  
# Environment configuration
environment: production

# Image configuration
image:
  backend:
    repository: astralearn/backend
    tag: "latest"
    pullPolicy: IfNotPresent
  frontend:
    repository: astralearn/frontend
    tag: "latest"
    pullPolicy: IfNotPresent

# Backend configuration
backend:
  enabled: true
  replicaCount: 3
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  service:
    type: ClusterIP
    port: 3001
    annotations: {}
  
  livenessProbe:
    httpGet:
      path: /api/health
      port: 3001
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /api/ready
      port: 3001
    initialDelaySeconds: 15
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
  
  env:
    NODE_ENV: production
    API_VERSION: v1
    ANALYTICS_ENABLED: true
    GAMIFICATION_ENABLED: true
    ADAPTIVE_LEARNING_ENABLED: true

# Frontend configuration
frontend:
  enabled: true
  replicaCount: 2
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "500m"
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  service:
    type: LoadBalancer
    port: 80
    httpsPort: 443
    annotations: {}
  
  livenessProbe:
    httpGet:
      path: /health
      port: 80
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /health
      port: 80
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# Database configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: ""
    username: "astralearn"
    password: ""
    database: "astralearn_prod"
  
  architecture: standalone
  primary:
    persistence:
      enabled: true
      size: 50Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
    
    configuration: |
      # PostgreSQL configuration for AstraLearn
      shared_buffers = 256MB
      effective_cache_size = 1GB
      work_mem = 4MB
      maintenance_work_mem = 64MB
      wal_buffers = 16MB
      checkpoint_completion_target = 0.9
      max_connections = 100
      log_min_duration_statement = 1000
      track_activities = on
      track_counts = on
      track_io_timing = on
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: true
    password: ""
  
  architecture: standalone
  master:
    persistence:
      enabled: true
      size: 10Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    
    configuration: |
      # Redis configuration for AstraLearn
      maxmemory 512mb
      maxmemory-policy allkeys-lru
      save 900 1
      save 300 10
      save 60 10000
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
  
  hosts:
    - host: astralearn.com
      paths:
        - path: /
          pathType: Prefix
    - host: app.astralearn.com
      paths:
        - path: /
          pathType: Prefix
  
  tls:
    - secretName: astralearn-tls
      hosts:
        - astralearn.com
        - app.astralearn.com

# Monitoring configuration
monitoring:
  enabled: true
  
  prometheus:
    enabled: true
    retention: "30d"
    storageSize: "20Gi"
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
  
  grafana:
    enabled: true
    storageSize: "5Gi"
    adminPassword: ""
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    
    dashboards:
      default:
        astralearn-overview:
          gnetId: 1860
          revision: 27
          datasource: Prometheus
        astralearn-learning:
          file: dashboards/learning-analytics.json
  
  loki:
    enabled: true
    retention: "336h"  # 2 weeks
    storageSize: "50Gi"
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
  
  promtail:
    enabled: true
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "500m"

# Security configuration
security:
  networkPolicies:
    enabled: true
  
  podSecurityPolicies:
    enabled: true
  
  rbac:
    create: true
  
  serviceAccount:
    create: true
    name: ""
    annotations: {}

# SSL/TLS configuration
ssl:
  enabled: true
  issuer: "letsencrypt-prod"
  dnsNames:
    - astralearn.com
    - www.astralearn.com
    - app.astralearn.com
    - api.astralearn.com

# External services configuration
externalServices:
  groq:
    apiKey: ""
    endpoint: "https://api.groq.com/openai/v1"
  
  smtp:
    host: ""
    port: 587
    username: ""
    password: ""
    secure: true

# Backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: "7d"
  storageClass: "standard"
  
  postgresql:
    enabled: true
    retention: "30d"
  
  redis:
    enabled: true
    retention: "7d"

# Feature flags
features:
  analytics: true
  gamification: true
  adaptiveLearning: true
  instructorTools: true
  aiIntegration: true
  realTimeUpdates: true
  mobileApp: false
  advancedReporting: true

# Performance configuration
performance:
  caching:
    enabled: true
    redis:
      ttl: 3600  # 1 hour
  
  cdn:
    enabled: false
    provider: ""
    domain: ""
  
  compression:
    enabled: true
    level: 6
